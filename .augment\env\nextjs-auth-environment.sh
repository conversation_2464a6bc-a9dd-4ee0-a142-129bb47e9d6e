#!/bin/bash
set -e

echo "Setting up Next.js Auth environment for remote agent..."

# Update package lists
sudo apt-get update

# Install Node.js and pnpm (if not already available in base environment)
echo "Ensuring Node.js and pnpm are available..."
npm install -g pnpm

# Configure Git with agent credentials
echo "Configuring Git credentials for agent..."
git config --global user.name "agent-kevin<PERSON><PERSON><PERSON>"
git config --global user.email "<EMAIL>"

# Set up environment variables for the session
echo "Setting up environment variables..."

# Resend configuration for email functionality
export RESEND_API_KEY="re_iXG7AmJM_9AtrDjEXJvnRHQ5m6CE9LrwH"
export RESEND_DOMAIN="updates.kevinlonigro.me"
export RESEND_SENDER="<EMAIL>"
export RESEND_RECIPIENT="<EMAIL>"

# NextAuth configuration
export NEXTAUTH_SECRET="f6def42e7e77e15a4622e9cdb7caf627650951a6faae10c72ea2ac6ab2adbba9"
export NEXTAUTH_URL="http://localhost:3000"

# OAuth provider credentials
export GOOGLE_CLIENT_ID="987752892869-cg79acnr9jcga3abvdbrgh4c7ct0he24.apps.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="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"
export GITHUB_ID="********************"
export GITHUB_SECRET="****************************************"
export AMAZON_CLIENT_ID="amzn1.application-oa2-client.3ad1afb609ef4d88aa27429b1df10ca1"
export AMAZON_CLIENT_SECRET="amzn1.oa2-cs.v1.dea5f822d23ec2fd51f657f81a5a570b4a78325b3163264f285049e3a97013ee"

# Database configuration
export DATABASE_URL="file:./dev.db"

# Cron job security
export CRON_SECRET="cron_8f2e9d4a7b1c6e3f9a2d5c8b4e7f1a9c3d6e2f5a8b1c4e7f9a2d5c8b4e7f1a9c"

# Add environment variables to the shell profile for persistence
echo "Making environment variables persistent..."
cat >> ~/.bashrc << 'EOF'

# Next.js Auth Project Environment Variables
export RESEND_API_KEY="re_iXG7AmJM_9AtrDjEXJvnRHQ5m6CE9LrwH"
export RESEND_DOMAIN="updates.kevinlonigro.me"
export RESEND_SENDER="<EMAIL>"
export RESEND_RECIPIENT="<EMAIL>"
export NEXTAUTH_SECRET="f6def42e7e77e15a4622e9cdb7caf627650951a6faae10c72ea2ac6ab2adbba9"
export NEXTAUTH_URL="http://localhost:3000"
export GOOGLE_CLIENT_ID="987752892869-cg79acnr9jcga3abvdbrgh4c7ct0he24.apps.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="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"
export GITHUB_ID="********************"
export GITHUB_SECRET="****************************************"
export AMAZON_CLIENT_ID="amzn1.application-oa2-client.3ad1afb609ef4d88aa27429b1df10ca1"
export AMAZON_CLIENT_SECRET="amzn1.oa2-cs.v1.dea5f822d23ec2fd51f657f81a5a570b4a78325b3163264f285049e3a97013ee"
export DATABASE_URL="file:./dev.db"
export CRON_SECRET="cron_8f2e9d4a7b1c6e3f9a2d5c8b4e7f1a9c3d6e2f5a8b1c4e7f9a2d5c8b4e7f1a9c"
EOF

# Navigate to workspace and install dependencies
echo "Installing project dependencies..."
cd /mnt/persist/workspace

# Install dependencies using pnpm
pnpm install

# Create .env.local file in the workspace
echo "Creating .env.local file..."
cat > .env.local << 'EOF'
GOOGLE_CLIENT_ID=987752892869-cg79acnr9jcga3abvdbrgh4c7ct0he24.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GITHUB_ID=********************
GITHUB_SECRET=****************************************
NEXTAUTH_SECRET=f6def42e7e77e15a4622e9cdb7caf627650951a6faae10c72ea2ac6ab2adbba9
NEXTAUTH_URL=http://localhost:3000
RESEND_API_KEY=re_iXG7AmJM_9AtrDjEXJvnRHQ5m6CE9LrwH
RESEND_DOMAIN=updates.kevinlonigro.me
RESEND_SENDER=<EMAIL>
RESEND_RECIPIENT=<EMAIL>
DATABASE_URL="file:./dev.db"
CRON_SECRET=cron_8f2e9d4a7b1c6e3f9a2d5c8b4e7f1a9c3d6e2f5a8b1c4e7f9a2d5c8b4e7f1a9c
AI_AGENT_EMAIL=<EMAIL>
AI_AGENT_GITHUB_TOKEN=****************************************
AMAZON_CLIENT_ID=amzn1.application-oa2-client.3ad1afb609ef4d88aa27429b1df10ca1
AMAZON_CLIENT_SECRET=amzn1.oa2-cs.v1.dea5f822d23ec2fd51f657f81a5a570b4a78325b3163264f285049e3a97013ee
EOF

echo "Environment setup complete!"
echo "Git user: $(git config --global user.name)"
echo "Git email: $(git config --global user.email)"
echo "RESEND_API_KEY: ${RESEND_API_KEY:0:10}..."
echo "RESEND_DOMAIN: $RESEND_DOMAIN"
echo "RESEND_SENDER: $RESEND_SENDER"
