# Project Memories

## Guideline Details

### App Router
App Router is preferred because it provides better performance and more intuitive routing.

### Server Components
Server Components is preferred to reduce client-side JavaScript and improve initial load times.

## Project History
- Started with Next.js 15.3.5
- Using Google OAuth for authentication
- Fixed authentication system mismatch: middleware and dashboard pages now use the same custom session system to prevent redirect errors

## Command Preferences
- Use direct pnpm commands (e.g., "pnpm dev", "pnpm build", etc) instead of prefixing with "cmd /c"
- In this Windows PowerShell environment, avoid PowerShell-specific commands or cmdlets (Get-Process, Set-ExecutionPolicy, etc.) for development tasks due to execution policy restrictions
- Use standard cross-platform commands or documented direct pnpm commands for package management, development servers, builds, and testing
- Note: The launch-process tool has output buffering issues that make pnpm commands appear to hang when using read-process, but commands execute successfully (verify with read-terminal)

## Development Workflow

### Local Agent Workflow
1. **Trigger**: When user starts message with "New Task", immediately create new branch from main
2. **Pre-Task**: Create new branch from main before starting work
   - **CRITICAL**: Always ensure main is up-to-date before branching:
     ```bash
     git checkout main
     git pull origin main  # Fetch latest changes
     git checkout -b new-feature-branch
     ```
   - **Why**: Prevents missing recent commits/fixes that were merged to main
   - **Example Issue**: Missing icon improvements, bug fixes, or other recent changes
3. **During Task**: Complete development and testing
4. **Post-Task**: Confirm with peer before proceeding with commit and PR to allow time for local review of changes
5. **After Confirmation**: Create commit and PR for deployment

### Remote Agent Workflow
- Continue with their existing workflow (no change from current process)

## Git Configuration

### Local Agent Configuration
- **Local Repository**: Configured with user credentials (kevinlonigro) for clean IDE experience
- **Local AI Agent Operations**: Uses temporary credential overrides via `-c` flags (agent-kevinlonigro) for commits without affecting local config
- **Command Example**: `git -c user.name="agent-kevinlonigro" -c user.email="<EMAIL>" commit -m "message"`

### Remote Agent Configuration
- **Remote Agent Environment**: Uses custom environment script (`.augment/env/nextjs-auth-environment.sh`) to configure git credentials globally
- **Git Credentials**: Set via `git config --global` in environment setup script
- **Environment Variables**: All project environment variables (Resend, NextAuth, OAuth providers) are configured in the environment script
- **Authentication**: Remote agents use GitHub OAuth integration configured in VS Code settings, not environment variables

## Screen Captures
- **Location**: `/screen_captures` folder in project root
- **Purpose**: AI agent saves screenshots here for user visibility during development and testing
- **Maintenance**: User cleans up folder periodically
- **Git**: Folder is ignored in `.gitignore` to prevent committing temporary screenshots

## Future Reminders
- **October 2025**: Revisit Auth.js v5 to reevaluate WebAuthn/Passkey implementation options for the NextAuth.js project, as v4 lacks WebAuthn support and v5 should be stable by then

