import type { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Terms of Service | React Next.js Learn Auth",
  description: "Terms of Service for React Next.js Learn Auth - A comprehensive authentication learning project built with Next.js 15, NextAuth.js, and Prisma.",
  robots: "index, follow",
  keywords: "terms of service, authentication, NextAuth.js, OAuth, privacy, legal, Next.js, learning project",
  authors: [{ name: "React Next.js Learn Auth" }],
  creator: "React Next.js Learn Auth",
  publisher: "React Next.js Learn Auth",
  openGraph: {
    title: "Terms of Service | React Next.js Learn Auth",
    description: "Terms of Service for React Next.js Learn Auth - A comprehensive authentication learning project built with Next.js 15, NextAuth.js, and Prisma.",
    type: "website",
    locale: "en_US",
    siteName: "React Next.js Learn Auth",
  },
  twitter: {
    card: "summary",
    title: "Terms of Service | React Next.js Learn Auth",
    description: "Terms of Service for React Next.js Learn Auth - A comprehensive authentication learning project built with Next.js 15, NextAuth.js, and Prisma.",
  },
};

export default function TermsOfService() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow-sm rounded-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Terms of Service</h1>
            <p className="text-gray-600">Last updated: {new Date().toLocaleDateString()}</p>
          </div>

          {/* Content */}
          <div className="prose prose-gray max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-700 mb-4">
                By accessing and using React Next.js Learn Auth (&quot;the Service&quot;), you accept and agree to be bound by the terms and provision of this agreement. This is a learning project designed to demonstrate modern authentication patterns using Next.js, NextAuth.js, and related technologies.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. Description of Service</h2>
              <p className="text-gray-700 mb-4">
                React Next.js Learn Auth is an educational authentication project that provides:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>OAuth authentication with Google, GitHub, and Amazon providers</li>
                <li>Passwordless email authentication via magic links</li>
                <li>Secure session management demonstrations</li>
                <li>User profile management capabilities</li>
                <li>Educational content about modern authentication patterns</li>
              </ul>
              <p className="text-gray-700 mb-4">
                This service is provided for educational and demonstration purposes only.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. User Responsibilities</h2>
              <p className="text-gray-700 mb-4">
                By using this service, you agree to:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>Provide accurate and truthful information when creating an account</li>
                <li>Maintain the security of your account credentials</li>
                <li>Use the service only for lawful and educational purposes</li>
                <li>Respect the intellectual property rights of others</li>
                <li>Not attempt to gain unauthorized access to the service or other users&apos; accounts</li>
                <li>Not use the service to transmit harmful, offensive, or illegal content</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Acceptable Use Policy</h2>
              <p className="text-gray-700 mb-4">
                You may not use this service to:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>Violate any applicable laws or regulations</li>
                <li>Infringe upon the rights of others</li>
                <li>Transmit spam, malware, or other harmful content</li>
                <li>Attempt to reverse engineer or compromise the service</li>
                <li>Use automated tools to access the service without permission</li>
                <li>Impersonate other users or entities</li>
                <li>Collect or harvest user information without consent</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Third-Party Authentication</h2>
              <p className="text-gray-700 mb-4">
                Our service integrates with third-party authentication providers including Google, GitHub, and Amazon. By using these authentication methods, you also agree to comply with the respective terms of service of these providers:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li><a href="https://policies.google.com/terms" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">Google Terms of Service</a></li>
                <li><a href="https://docs.github.com/en/site-policy/github-terms/github-terms-of-service" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">GitHub Terms of Service</a></li>
                <li><a href="https://aws.amazon.com/service-terms/" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">Amazon Web Services Terms</a></li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Account Termination</h2>
              <p className="text-gray-700 mb-4">
                We reserve the right to terminate or suspend your account at any time, with or without notice, for conduct that we believe violates these Terms of Service or is harmful to other users, us, or third parties, or for any other reason.
              </p>
              <p className="text-gray-700 mb-4">
                You may terminate your account at any time by contacting us or by deleting your account through the service interface when available.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Disclaimer of Warranties</h2>
              <p className="text-gray-700 mb-4">
                This service is provided &quot;as is&quot; and &quot;as available&quot; without any warranties of any kind, either express or implied. We do not warrant that the service will be uninterrupted, error-free, or completely secure.
              </p>
              <p className="text-gray-700 mb-4">
                As this is an educational project, it may contain bugs, security vulnerabilities, or other issues that would not be acceptable in a production environment.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Limitation of Liability</h2>
              <p className="text-gray-700 mb-4">
                In no event shall we be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the service.
              </p>
              <p className="text-gray-700 mb-4">
                Our total liability to you for all claims arising from or relating to the service shall not exceed $100.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">9. Governing Law and Dispute Resolution</h2>
              <p className="text-gray-700 mb-4">
                These Terms of Service shall be governed by and construed in accordance with the laws of the United States, without regard to its conflict of law provisions.
              </p>
              <p className="text-gray-700 mb-4">
                Any disputes arising from these terms or your use of the service shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">10. Changes to Terms</h2>
              <p className="text-gray-700 mb-4">
                We reserve the right to modify these Terms of Service at any time. We will notify users of any material changes by posting the new Terms of Service on this page and updating the &quot;Last updated&quot; date.
              </p>
              <p className="text-gray-700 mb-4">
                Your continued use of the service after any such changes constitutes your acceptance of the new Terms of Service.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">11. Contact Information</h2>
              <p className="text-gray-700 mb-4">
                If you have any questions about these Terms of Service, please contact us at:
              </p>
              <p className="text-gray-700 mb-4">
                Email: <EMAIL><br />
                Project Repository: <a href="https://github.com/kevinlonigro/react-nextjs-learn-auth" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">GitHub</a>
              </p>
            </section>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <Link
                href="/privacy"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                View Privacy Policy →
              </Link>
              <Link
                href="/"
                className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                  />
                </svg>
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
